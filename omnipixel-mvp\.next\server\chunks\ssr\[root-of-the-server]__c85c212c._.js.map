{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { cookies } from 'next/headers'\r\n\r\nexport async function createClient(url?:string, key?:string) {\r\n  const cookieStore = await cookies()\r\n  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!\r\n  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\n  return createServerClient(\r\n    url,\r\n    key,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            )\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe,aAAa,GAAW,EAAE,GAAW;IACzD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM;IACN,MAAM;IAEN,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/admin/client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/admin/client.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/admin/client.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/admin/client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/admin/client.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/admin/client.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,kCACA", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/admin/page.tsx"], "sourcesContent": ["// app/admin/page.tsx\nimport { redirect } from 'next/navigation'\nimport { createClient } from '@/utils/supabase/server'\nimport AdminClient from './client'\nimport { Build, Project } from '@/lib/supabase'\n\ninterface RawProject {\n  id: string\n  name: string\n  auto_release:boolean\n  stream_project_id: string\n  user_id: string\n  config: any\n  created_at: string\n  updated_at: string\n  profiles?: {\n    email: string\n    role: string\n  } | null\n  builds: Array<{\n    id: string\n    filename: string\n    original_filename?: string\n    version: number\n    created_at: string\n  }>\n}\n\n// Helper: Normalize project structure for client use (to match original types)\nfunction normalizeProjects(rawProjects: RawProject[]): RawProject[] {\n  return rawProjects.map((p) => ({\n    id: p.id,\n    name: p.name,\n    stream_project_id: p.stream_project_id,\n    user_id: p.user_id,\n    config: p.config,\n    created_at: p.created_at,\n    updated_at: p.updated_at,\n    auto_release: p.auto_release,\n    profiles: {\n      email: p.profiles?.email || '',\n      role: p.profiles?.role || '',\n    },\n    builds: (p.builds || []).map((b: Build) => ({\n      id: b.id,\n      filename: b.filename,\n      original_filename: b.original_filename,\n      version: b.version,\n      created_at: b.created_at,\n    })),\n  }))\n}\n\nexport default async function AdminPage() {\n  const supabase = await createClient()\n\n  // Auth\n  const { data: { user } } = await supabase.auth.getUser()\n  if (!user) redirect('/login')\n\n  // Profile\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', user.id)\n    .single()\n  if (!profile || profile.role !== 'platform_admin') redirect('/dashboard')\n\n\n  // Projects (with builds & profiles)\n  const { data: rawProjects, error }:{ data: RawProject[] | null; error: any } = await supabase\n    .from('projects')\n    .select(`\n      id, name, stream_project_id, user_id, config, created_at, updated_at,\n      profiles:profiles!projects_user_id_fkey(email, role),\n      builds(id, filename, original_filename, version, created_at)\n    `)\n    .order('created_at', { ascending: false })\n\n  const projects = normalizeProjects(rawProjects || [])\n\n  return (\n    <AdminClient\n      user={user}\n      profile={profile}\n      initialProjects={projects}\n    />\n  )\n}\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;AACrB;AAAA;AACA;AACA;;;;;AAyBA,+EAA+E;AAC/E,SAAS,kBAAkB,WAAyB;IAClD,OAAO,YAAY,GAAG,CAAC,CAAC,IAAM,CAAC;YAC7B,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,IAAI;YACZ,mBAAmB,EAAE,iBAAiB;YACtC,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,MAAM;YAChB,YAAY,EAAE,UAAU;YACxB,YAAY,EAAE,UAAU;YACxB,cAAc,EAAE,YAAY;YAC5B,UAAU;gBACR,OAAO,EAAE,QAAQ,EAAE,SAAS;gBAC5B,MAAM,EAAE,QAAQ,EAAE,QAAQ;YAC5B;YACA,QAAQ,CAAC,EAAE,MAAM,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAa,CAAC;oBAC1C,IAAI,EAAE,EAAE;oBACR,UAAU,EAAE,QAAQ;oBACpB,mBAAmB,EAAE,iBAAiB;oBACtC,SAAS,EAAE,OAAO;oBAClB,YAAY,EAAE,UAAU;gBAC1B,CAAC;QACH,CAAC;AACH;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,OAAO;IACP,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,IAAI,CAAC,MAAM,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IAEpB,UAAU;IACV,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IACT,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,kBAAkB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IAG5D,oCAAoC;IACpC,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAA6C,MAAM,SAClF,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;IAIT,CAAC,EACA,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,MAAM,WAAW,kBAAkB,eAAe,EAAE;IAEpD,qBACE,8OAAC,uHAAA,CAAA,UAAW;QACV,MAAM;QACN,SAAS;QACT,iBAAiB;;;;;;AAGvB", "debugId": null}}]}